{{ define "main" }}


{{ partial "navbar.html" . }}
{{ partial "navbar-clone.html" . }}

<section class="section is-medium" id="features">
  <div class="container">
    <div class="columns">
      <div class="column is-centered-tablet has-text-centered">
        <h1 class="title section-title">{{ .Title }}</h1>
        <h5 class="subtitle is-5 is-muted">{{ .Params.Subtitle }}</h5>
        <div class="divider is-centered"></div>
      </div>
    </div>

    <!-- Features Content -->
    <div class="content">
      {{ .Content }}
    </div>

    <!-- Feature Categories Grid (if we have individual feature pages later) -->
    {{ if .Pages }}
    <div class="features-grid" style="display: flex; flex-wrap: wrap; gap: 2rem; justify-content: space-between; margin-top: 3rem;">
      {{ range .Pages }}
      <div class="feature-item" style="flex: 0 0 calc(33.333% - 1.5rem); display: flex;">
        <div class="card" style="display: flex; flex-direction: column; width: 100%; height: 100%;">
          {{ if .Params.icon }}
          <div class="card-content has-text-centered" style="padding-bottom: 0;">
            <span class="icon is-large has-text-primary">
              <i class="{{ .Params.icon }} fa-3x"></i>
            </span>
          </div>
          {{ end }}

          <div class="card-content" style="display: flex; flex-direction: column; flex-grow: 1;">
            <div class="media">
              <div class="media-content">
                <p class="title is-4">
                  <a href="{{ .Permalink }}" class="has-text-dark">{{ .Title }}</a>
                </p>
                {{ if .Params.category }}
                <p class="subtitle is-6 has-text-grey">
                  <span class="tag is-light">{{ .Params.category }}</span>
                </p>
                {{ end }}
              </div>
            </div>

            <div class="content" style="flex-grow: 1; display: flex; flex-direction: column;">
              <div style="flex-grow: 1;">
                {{ if .Params.summary }}
                  <p>{{ .Params.summary }}</p>
                {{ else }}
                  <p>{{ .Summary }}</p>
                {{ end }}
              </div>

              <div style="margin-top: auto;">
                <a href="{{ .Permalink }}" class="button primary-btn raised">
                  <span class="icon">
                    <i class="fas fa-arrow-right"></i>
                  </span>
                  <span>Learn More</span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
      {{ end }}
    </div>
    {{ end }}

  </div>
</section>

{{ partial "footer.html" . }}

{{ end }}
