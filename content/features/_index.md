---
title: "Features"
subtitle: "A comprehensive overview of whitesky.cloud's private managed cloud platform"
description: "Explore the complete feature set of whitesky.cloud's private managed cloud platform including Infrastructure as a Service, S3 storage, Kubernetes, and more."
---

## Infrastructure as a Service

### Cloudspaces

A cloudspace is a customer-owned layer 2 network with a virtual firewall in which virtual machines can be deployed. The virtual firewall has 0 or more external networks and acts as a gateway to the external networks.

**Key Features:**
- Bound to a cloud location
- Isolated layer 2 network
- **Firewall Options:**
  - **Built-in:**
    - DHCP server
    - Cloud init
    - Port forwards
    - Routing
    - VPN access to cloudspace
    - Automated VPNs between Cloudspaces (Connected Cloudspaces)
  - **Custom:** Any virtualized firewall deployed in a virtual machine can act as the firewall for a cloudspace
- 0 or more external networks (internet, customer network, etc.)
- **Ingress:**
  - Server pools
  - **Load balancing:**
    - (Layer 7) Reverse proxies, SSL offloading, Let's Encrypt support
    - (Layer 3) TCP load balancers, SSL offloading or pass through, UDP load balancers

### Virtual Machines

**Deployment Options:**
- Bound to a cloudspace
- **Create from:**
  - Predefined images
  - Install from ISO image
  - Clone from a snapshot
  - **Import from:**
    - Veeam
    - Acronis
    - whitesky backup

**Storage:**
- **Software defined:**
  - vDisks based on integrated whitesky software defined storage (not Ceph!) with or without NVME local cache
  - Automated vDisk snapshots on software defined storage every hour (configurable retention)
- vDisks via direct attached NVME for high demanding IO workloads
- CDRom images
- Integrated backup to S3 storage

**Advanced Features:**
- VGPU support (both virtualized and dedicated GPU)
- CPU pinning & custom CPU topologies
- Anti-affinity groups
- **Networking:**
  - Default cloudspace network interface (VMs never reachable from outside by default)
  - Extra interfaces to other cloudspaces
  - Extra interfaces to external networks
- Audits
- Dynamic sizing for memory, CPU and vDisks
- **Cloud init:**
  - Virtual machine initialization
  - Cloud init templates for initializing extra software
- Automation via QEMU agent (get/set files in VM, exec programs)
- Performance stats and spending history
- Microsoft software licensing overview
- Create VM Template
- **VM migration:**
  - Import/export via S3 bucket
  - VM copy (whitesky to whitesky) via CLI
  - Nearly online migration from everywhere to whitesky
  - Backup/restore via whitesky Backup

## S3 Compatible Storage

### Objectspaces

**Core Features:**
- Bound to a cloud location
- **Security:**
  - Admin access key/secret key
  - Bucket level keys for read, write or read-write access
- Object locking support
- Can grow up to 360 PB
- Accessible via the cloudspace network privately or exposed via cloudspace ingress
- Dynamic throughput control

## Kubernetes as a Service

### Containerspaces

**Managed Kubernetes Features:**
- Automated Rancher deployment in cloudspaces
- Multi cloud location kubernetes via connected cloudspaces
- Automated extension
- Integrated load balancers via cloudspace ingress
- **Automated installation:**
  - Ingress controller
  - Certificate manager
  - Automated installation of CSI driver for software defined storage and direct attached NVME

## Portal

The whitesky portal is a white-labeled self-service cloud portal that allows internal or external cloud customers to manage their cloud resources across available cloud locations.

### Cloud Admin: Customer Management and Administration

**Customer Management:**
- Create/accept/delete customers
- Resource pricing, location access, resource quotas
- Consult customer invoices

**Billing:**
- Generate monthly invoices for cloud customers
- For cloud resource consumption
- For Microsoft licensed software (daily automated scanning with Octopus.cloud for SPLA & CSP)

**Administration:**
- **Locations:** Manage location resource standard pricing
- **Notifications:** Send notifications to customers
- **Audits:** Review customer audit records
- **Sales analysis:** Review customer cloud resource consumption evolution
- **Software licenses:** Examine Microsoft license software usage by customer calc resources

**Settings:**
- General: live chat, self-registration + payment + approval, notifications forwarding, show/hide pricing, license compliance issue notifications
- SPLA: Microsoft SPLA license config
- Invoice settings
- Portal branding
- Payment provider
- Audit record forwarding
- DNS resource settings
- Default resource quotas for new customers
- Support config
- Emergency notifications
- **License compliance:** Overview of Windows VMs that cannot be automatically scanned for Microsoft licensed software

### Customer Admin Section

**SSL Certificate Management:**
- Customer SSL certificate store for use in reverse proxies and kubernetes clusters
- Add SSL certificates
- Update SSL certificates (updates the SSL certificate in the store and in resources that use them)

**Access Control:**
- **Roles:** Define roles and permissions for fine-grained resource access
- **DNS:** DNS settings for automated DNS records for cloud resources
- **License notifications:** Get warnings when users deploy Microsoft licensed software
- **Audits:** Review audit records
- **Emergency notifications:** Get notified when cloud resources become unavailable

### Identity and Access Management

**Security Features:**
- Separate deployed OpenID/OAUTH2 Identity access manager
- **2FA always required via:**
  - Email
  - Phone number (SMS)
  - Authenticator
- Hierarchical Organization based
- JWT support
- Self-registration
- SSO via Microsoft Account/Google Account (coming soon)

### Recycle Bin

**Data Protection:**
- Soft deleted cloud resources (cloudspaces, objectspaces, virtual machines, buckets, vDisks, VM Images, CDROM images, vGPUs) remain in the recycle bin for 7 days before permanent deletion
- Ability to empty recycle bin

### Tools and APIs

**Developer Tools:**
- 100% complete API (UI is built on top of the API)
- UI shows which APIs it used for building the user interface
- Swagger UI for exploring the API
- CLI for Windows, Mac and Linux
- Terraform provider
- Ansible support in opensource

## Platform

### whitesky cloud location (aka the G8)

<figure class="image" style="margin: 2rem 0; text-align: center;">
  <img src="/images/Hyper-converged-G8-setup.webp" alt="Hyper-converged G8 Setup Architecture" style="max-width: 100%; height: auto; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
</figure>

**Managed Service:**
- Managed by whitesky as a service
  - Installation
  - Update/upgrade/troubleshooting
  - 24/7 monitoring

**Architecture:**
- Deployed in blocks of up to 6 servers (always n+1 spare server)
- Servers in each block have the same hardware configuration
- Blocks can provide compute, block storage, object storage or a combination
- **Minimal size:** 1 block with 3 servers
- **Maximum size:** not defined

**Software Defined Storage:**
- In-house developed, 100% part of the platform
- Uses erasure coding for maximum data durability with minimal overhead
- vDisk live migration to support server maintenance or IO rebalancing
- Support for flash backend or HDD backend with flash cache
- Used for both vDisk (log structured storage) and S3 compatible storage

**Infrastructure Features:**
- Self-healing infrastructure
- **Seamless updates:** VM and vDisk live migration combined with block-based server deployment allows seamless updates of everything, including the host OS

### whitesky cloud location manager (aka meneja)

**Central Management:**
- Central management UI for managing cloud locations
- **Monitoring:**
  - Capacity planning
  - Performance indicators
- Configuration management
- Virtual machine & CD-ROM image distribution
- External network management
- GPU management

**Image Management:**
- Virtual machine & CD-ROM image management, validation and distribution
- Reseller management & billing
