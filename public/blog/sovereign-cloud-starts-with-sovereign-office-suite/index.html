<!DOCTYPE html>
<html lang="en-us">
  <head><script src="/livereload.js?mindelay=10&amp;v=2&amp;port=1313&amp;path=livereload" data-no-instant defer></script>
    

<meta property="og:url" content="http://localhost:1313/blog/sovereign-cloud-starts-with-sovereign-office-suite/">
  <meta property="og:site_name" content="whitesky cloud platform">
  <meta property="og:title" content="Sovereign Cloud Starts with a Sovereign Office Suite">
  <meta property="og:description" content="When we think of a sovereign cloud, we often associate it with digital independence—the ability to control who can and cannot access our digital assets. The best place to start is with your digital office environment.">
  <meta property="og:locale" content="en_us">
  <meta property="og:type" content="article">
    <meta property="article:section" content="blog">
    <meta property="article:published_time" content="2025-03-03T00:00:00+00:00">
    <meta property="article:modified_time" content="2025-03-03T00:00:00+00:00">
    <meta property="article:tag" content="Sovereign-Cloud">
    <meta property="article:tag" content="Data-Sovereignty">
    <meta property="article:tag" content="Nextcloud">
    <meta property="article:tag" content="Mail-Server">
    <meta property="article:tag" content="Office-Suite">


<meta name="description" content="When we think of a sovereign cloud, we often associate it with digital independence—the ability to control who can and cannot access our digital assets. The best place to start is with your digital office environment." />
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<meta http-equiv="x-ua-compatible" content="ie=edge">
    <title>whitesky cloud platform</title>
    
  
<link
  rel="stylesheet"
  href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css"
  integrity="sha512-Avb2QiuDEEvB4bZJYdft2mNjVShBftLdPG8FJ0V7irTLQ8Uo0qcPxh4Plq7G5tGm0rU+1SPhVotteLpBERwTkw=="
  crossorigin="anonymous"
  referrerpolicy="no-referrer"
/>
<link rel="icon" type="image/png" href="/images/favicon.png" />
<link href="https://fonts.googleapis.com/css?family=Open&#43;Sans:400,600" rel="stylesheet">
<link rel="stylesheet" type="text/css" href="/css/style.css">
<link rel="stylesheet" type="text/css" href="/css/icons.css">

  </head>
  <body>
    
    
    <div id="preloader">
      <div id="status"></div>
    </div>
    

    


<nav class="navbar is-fresh is-transparent no-shadow" role="navigation" aria-label="main navigation">
  <div class="container">
    <div class="navbar-brand">
      <a class="navbar-item" href="/">
        <img src="/images/logo-blue-blacktext-small.png" alt="" width="160" height="28">
      </a>

      <a role="button" class="navbar-burger" aria-label="menu" aria-expanded="false" data-target="navbar-menu">
        <span aria-hidden="true"></span>
        <span aria-hidden="true"></span>
        <span aria-hidden="true"></span>
      </a>
    </div>

      <div id="navbar-menu" class="navbar-menu is-static">

        <div class="navbar-end">
          <a href="/features" class="navbar-item is-secondary">
            Features
          </a>
          <a href="/" class="navbar-item is-secondary">
            Pricing
          </a>
          <a href="/blog#blog" class="navbar-item is-secondary">
            Blog
          </a>
          <div class="navbar-item has-dropdown is-hoverable">
            <a class="navbar-link">
              Resources
            </a>

            <div class="navbar-dropdown">
              <a href="/" class="navbar-item">
                Documentation
              </a>
              <a href="/" class="navbar-item">
                Support
              </a>
              <a href="/partner-portal" class="navbar-item">
                Partner Portal
              </a>
            </div>
          </div>
          <a href="/" class="navbar-item is-secondary">
            Log in
          </a>
          <a href="/" class="navbar-item">
            <span class="button signup-button rounded secondary-btn raised">
              Try now
            </span>
          </a>
        </div>
      </div>
  </div>
</nav>


<nav id="navbar-clone" class="navbar is-fresh is-transparent" role="navigation" aria-label="main navigation">
  <div class="container">
    <div class="navbar-brand">
      <a class="navbar-item" href="/">
        <img src="/images/logo-blue-blacktext-small.png" alt="" width="160" height="160">
      </a>

      <a role="button" class="navbar-burger" aria-label="menu" aria-expanded="false" data-target="cloned-navbar-menu">
        <span aria-hidden="true"></span>
        <span aria-hidden="true"></span>
        <span aria-hidden="true"></span>
      </a>
    </div>

    <div id="cloned-navbar-menu" class="navbar-menu is-fixed">

      <div class="navbar-end">
        <a href="/features" class="navbar-item is-secondary">
          Features
        </a>
        <a href="/" class="navbar-item is-secondary">
          Pricing
        </a>
        <a href="/blog#blog" class="navbar-item is-secondary">
          Blog
        </a>
        <div class="navbar-item has-dropdown is-hoverable">
          <a class="navbar-link">
            Resources
          </a>

          <div class="navbar-dropdown">
            <a href="/" class="navbar-item">
              Documentation
            </a>
            <a href="/" class="navbar-item">
              Support
            </a>
            <a href="/partner-portal" class="navbar-item">
              Partner Portal
            </a>
          </div>
        </div>
        <a href="/" class="navbar-item is-secondary">
          Log in
        </a>
        <a href="/" class="navbar-item">
          <span class="button signup-button rounded secondary-btn raised">
            Try now
          </span>
        </a>
      </div>
    </div>
</div>
</nav>


<section class="section is-medium">
  <div class="container">
    
    <div class="columns">
      <div class="column is-8 is-offset-2">
        <div class="has-text-centered">
          <h1 class="title is-2 has-text-weight-bold">Sovereign Cloud Starts with a Sovereign Office Suite</h1>
          
          <h2 class="subtitle is-4 has-text-grey">Taking back control of your data by reclaiming ownership of your digital office environment</h2>
          
          
          <div class="article-meta">
            <p class="has-text-grey">
              <time datetime="2025-03-03">
                March 3, 2025
              </time>
              
              <span class="has-text-grey-light"> • </span>
              <span>Geert Audenaert</span>
              
              <span class="has-text-grey-light"> • </span>
              <span>5 min read</span>
            </p>
          </div>
          
          
          <div class="tags is-centered" style="margin-top: 1rem;">
            
            <span class="tag is-primary is-light">sovereign-cloud</span>
            
            <span class="tag is-primary is-light">data-sovereignty</span>
            
            <span class="tag is-primary is-light">nextcloud</span>
            
            <span class="tag is-primary is-light">mail-server</span>
            
            <span class="tag is-primary is-light">office-suite</span>
            
          </div>
          
        </div>
        
        <div class="divider  is-centered" style="margin-top: 1rem;"></div>
      </div>
    </div>

    
    
    <div class="columns">
      <div class="column is-10 is-offset-1">
        <figure class="image">
          <img src="/images/blogs/sovereign-cloud.webp" alt="Sovereign Cloud Starts with a Sovereign Office Suite" style="border-radius: 6px;">
        </figure>
      </div>
    </div>
    

    
    <div class="columns">
      <div class="column is-8 is-offset-2">
        <div class="content is-medium">
          <h1 id="sovereign-cloud-starts-with-a-sovereign-office-suite">Sovereign Cloud Starts with a Sovereign Office Suite</h1>
<p>When we think of a sovereign cloud, we often associate it with digital independence—the ability to control who can and cannot access our digital assets.</p>
<p>Over the years, most of us have embraced Google Workspace or Microsoft 365. They&rsquo;re easy to use, always available, and accessible from anywhere. It&rsquo;s a seamless experience. But in exchange for that convenience, we&rsquo;ve given up control over our data.</p>
<p>We trust these providers to handle our information responsibly, and until recently, that trust may have been well-placed. But times are changing—fast.</p>
<p>If you truly want to take back control of our data, the best place to start is with your digital office environment. By reclaiming ownership of your office suite, you can show the entire organization why full data sovereignty matters—and take the first step toward a truly sovereign cloud.</p>
<h2 id="how-difficult-is-it">How Difficult Is It?</h2>
<p>To understand what&rsquo;s available today for building a sovereign office setup, I spent the last few days setting up a fully functional demonstration environment.</p>
<p>Here&rsquo;s what I needed:</p>
<ul>
<li><strong>Email</strong> – Accessible via web interface, phone, and desktop client</li>
<li><strong>File sharing</strong></li>
<li><strong>Video conferencing &amp; chat</strong></li>
<li><strong>Productivity tools:</strong>
<ul>
<li>Word processor</li>
<li>Spreadsheets</li>
<li>Presentation builder</li>
</ul>
</li>
</ul>
<p>The biggest challenge? Setting up a proper email server. Email requires configuring multiple protocols like DKIM and DMARC, which can be intimidating to get right.</p>
<p>For the rest, I wanted an all-in-one system where everything integrates seamlessly.</p>
<p>For email, I chose <strong>Mail-in-a-Box</strong> from a longer list of options. I picked it because it also acts as a DNS server and claims to handle the tricky configurations automatically by creating the necessary DNS records itself.</p>
<p>For everything else, I went with <strong>Nextcloud</strong>. It&rsquo;s been getting a lot of attention lately, and I wanted to see for myself what it can do.</p>
<h2 id="the-architecture-i-chose-for-this-experiment">The Architecture I Chose for This Experiment</h2>
<p>The setup consists of:</p>
<ul>
<li><strong>Cloudspace</strong> – A <strong>whitesky cloudspace</strong> is a private Layer 2 network, isolated from external networks by a built-in virtual firewall.</li>
<li><strong>Two Virtual Machines (VMs)</strong> – One for the mail server and one for Nextcloud.</li>
<li><strong>Objectspace</strong> – A <strong>whitesky objectspace</strong> is an S3-compatible object storage provider that connects to the private network of a cloudspace.</li>
</ul>
<h3 id="how-it-works">How It Works</h3>
<ul>
<li>The <strong>mail server</strong> acts as the <strong>Mail Transfer Agent (MTA)</strong> and serves as the central authentication source for Nextcloud.</li>
<li>It supports standard mail protocols like <strong>SMTP, POP3, and IMAP</strong> and includes a web portal for administration and email access.</li>
<li>For webmail, the easiest option is to use the built-in mail client in <strong>Nextcloud&rsquo;s web interface</strong>.</li>
</ul>
<p>The <strong>Nextcloud server</strong> stores all files in an <strong>Objectspace bucket</strong>, making the setup highly scalable from the start.</p>
<p>Both the Nextcloud and mail servers are managed via <strong>SSH over a WireGuard VPN tunnel</strong> that was added to the Cloudspace.</p>
<h2 id="setting-up-the-mail-server">Setting Up the Mail Server</h2>
<p>The first step in setting up a mail server is configuring DNS. Normally, this can be complex, but <strong>Mail-in-a-Box</strong> makes it easy by acting as its own DNS server, handling most of the setup automatically.</p>
<p>The installation process includes built-in <strong>pre-flight checks</strong>, ensuring things like <strong>PTR records</strong> are correctly set up. It then walks you through the entire setup, step by step.</p>
<p>It took me about two hours—I made a mistake and had to restart—but the effort was well worth it.</p>
<p>Now, I have my <strong>own sovereign, independent mail server</strong>, and it works flawlessly!</p>
<p>Want to test it? Send me an email at <strong><a href="mailto:<EMAIL>"><EMAIL></a></strong>—I&rsquo;ll receive it, and you&rsquo;ll get a reply. No emails land in spam, and all security certificates are automatically handled by Let&rsquo;s Encrypt.</p>
<p>Managing users and aliases is straightforward, and the built-in webmail works great. For my desktop and phone, I use Thunderbird, which configured my account automatically.</p>
<h2 id="setting-up-nextcloud">Setting Up Nextcloud</h2>
<p>After evaluating different options, I chose to use the <strong>Docker-based Nextcloud All-In-One</strong> installation method.</p>
<p>This approach starts by deploying the Nextcloud All-In-One Docker container, which provides a user-friendly interface where you can select the components you want in your Nextcloud setup.</p>
<p>Once everything is configured, just hit <strong>Install</strong>, and within <strong>15 minutes</strong>, all the necessary components are <strong>downloaded, running, and fully initialized</strong>—ready to use.</p>
<h2 id="integrating-authentication-and-storage">Integrating Authentication and Storage</h2>
<p>To streamline user management, I installed the <strong>External Authentication App</strong> in Nextcloud. This allows <strong>me to add users only on the mail server</strong>, and Nextcloud will authenticate them against it.</p>
<p>A more robust approach would be to use <strong>LDAP</strong>, so both the mail server and Nextcloud authenticate against a single directory service. However, <strong>Mail-in-a-Box doesn&rsquo;t support LDAP</strong>. Other mail solutions do, but they can be trickier to configure from a DNS perspective.</p>
<p>To set up external authentication, I had to log into the Nextcloud server and access the Nextcloud Docker container to configure authentication manually. Even so, it was up and running in just 5 minutes.</p>
<h3 id="configuring-scalable-storage-with-objectspaces">Configuring Scalable Storage with Objectspaces</h3>
<p>Next, I <strong>deployed an Objectspace</strong> within the same Cloudspace and configured Nextcloud to use Objectspace as its <strong>primary storage</strong>.</p>
<p>Why?</p>
<ul>
<li>Like this, Nextcloud only stores metadata, while files are stored in an S3-compatible object storage (Objectspace).</li>
<li>This makes the Nextcloud server <strong>highly scalable</strong>, as it doesn&rsquo;t handle file storage directly.</li>
</ul>
<p>To make this work, I had to access the Nextcloud Docker container again to modify the configuration manually.</p>
<h2 id="day-2-operations-backup--upgrades">Day 2 Operations: Backup &amp; Upgrades</h2>
<p><strong>Backup</strong>
Both <strong>Mail-in-a-Box</strong> and <strong>Nextcloud</strong> come with <strong>built-in backup mechanisms</strong>, making it easy to protect and restore data if needed.</p>
<p><strong>Upgrades</strong>
Keeping the system up to date is just as simple:</p>
<ul>
<li><strong>Mail-in-a-Box</strong> has a streamlined update process that ensures all components stay secure and functional.</li>
<li><strong>Nextcloud</strong> also offers an <strong>easy upgrade path</strong>, allowing you to apply updates without hassle.</li>
</ul>
<p>With both solutions, <strong>maintenance is minimal</strong>, ensuring <strong>a secure and up-to-date sovereign office suite</strong> with minimal effort.</p>
<h2 id="its-not-difficult">It&rsquo;s Not Difficult!</h2>
<p>In just a few hours, I had my sovereign office suite up and running—and I was blown away by the quality of both Mail-in-a-Box and Nextcloud.</p>
<p>Every application I needed was available and worked flawlessly. In fact, I&rsquo;m writing this very blog post in the web-based word processor that comes standard with Nextcloud. And it feels just as smooth as the Office 365 editor I&rsquo;m used to.</p>
<p>But there&rsquo;s <strong>one big difference—I know exactly where my data is stored!</strong></p>

        </div>
        
        
        <div class="article-footer" style="margin-top: 3rem; padding-top: 2rem; border-top: 1px solid #dbdbdb;">
          <div class="level">
            <div class="level-left">
              
              <div class="level-item">
                <div class="media">
                  <div class="media-content">
                    <p class="title is-6">Author: Geert Audenaert</p>
                  </div>
                </div>
              </div>
              
            </div>
            <div class="level-right">
              <div class="level-item">
                <div class="field is-grouped">
                  <p class="control">
                    <a class="button primary-btn raised" href="/blog">
                      <span class="icon">
                        <i class="fas fa-arrow-left"></i>
                      </span>
                      <span>Back to Blogs</span>
                    </a>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    
    
    
    <div class="columns" style="margin-top: 4rem;">
      <div class="column is-10 is-offset-1">
        <h3 class="title is-4 has-text-centered">Related Articles</h3>
        <div class="divider"></div>
        
        <div class="columns is-multiline">
          
          <div class="column is-one-third">
            <div class="card">
              
              <div class="card-image">
                <figure class="image is-16by9">
                  <img src="/images/blogs/ai.webp" alt="Why AI Should Be Private: A Call for Proactive Measures in the Digital Age">
                </figure>
              </div>
              
              
              <div class="card-content">
                <p class="title is-6">
                  <a href="http://localhost:1313/blog/why-ai-should-be-private/" class="has-text-dark">Why AI Should Be Private: A Call for Proactive Measures in the Digital Age</a>
                </p>
                <p class="subtitle is-7 has-text-grey">
                  February 7, 2025
                </p>
                
                <p class="is-size-7">In the era of rapid technological advancement, Artificial Intelligence stands at the forefront, …</p>
                
              </div>
            </div>
          </div>
          
        </div>
      </div>
    </div>
    
  </div>
</section>



<footer class="footer footer-dark">
  <div class="container">
    <div class="columns">
      <div class="column">
        <div class="footer-logo">
          <img src="/images/logos/ws_logo_white_vertical_v1.webp">
        </div>
      </div>
        <div class="column">
          <div class="footer-column">
            <div class="footer-header">
                <h3>Product</h3>
            </div>
            <ul class="link-list">
              <li>
                <a href="/features">
                  Discover features
                </a>
              </li>
              <li>
                <a href="/">
                  Why choose our product?
                </a>
              </li>
              <li>
                <a href="/">
                  Compare features
                </a>
              </li>
              <li>
                <a href="/">
                  Our roadmap
                </a>
              </li>
              <li>
                <a href="/agb">
                  AGB
                </a>
              </li>
            </ul>
          </div>
        </div>
        <div class="column">
          <div class="footer-column">
            <div class="footer-header">
                <h3>Docs</h3>
            </div>
            <ul class="link-list">
              <li>
                <a href="/">
                  Get started
                </a>
              </li>
              <li>
                <a href="/">
                  User guides
                </a>
              </li>
              <li>
                <a href="/">
                  Admin guide
                </a>
              </li>
              <li>
                <a href="/">
                  Developers
                </a>
              </li>
            </ul>
          </div>
        </div>
        <div class="column">
          <div class="footer-column">
            <div class="footer-header">
                <h3>Blog</h3>
            </div>
            <ul class="link-list">
              <li>
                <a href="/blog/first">
                  Latest news
                </a>
              </li>
              <li>
                <a href="/blog/second">
                  Tech articles
                </a>
              </li>
            </ul>
          </div>
        </div>
      <div class="column">
        <div class="footer-column">
          <div class="footer-header">
            <h3>Follow Us</h3>
            <nav class="level is-mobile">
              <div class="level-left">
                <a class="level-item" href="https://www.facebook.com/whitesky.fb" target="_blank">
                  <span class="icon"><i class="fa fa-facebook"></i></span>
                </a>
                <a class="level-item" href="https://twitter.com/StefMa91" target="_blank">
                  <span class="icon"><i class="fa fa-twitter"></i></span>
                </a>
                <a class="level-item" href="https://www.linkedin.com/company/whitesky-cloud" target="_blank">
                  <span class="icon"><i class="fa fa-linkedin"></i></span>
                </a>
              </div>
            </nav>
          </div>
        </div>
      </div>
    </div>
  </div>
</footer>





    
    <div id="backtotop"><a href="#"></a></div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.1/jquery.min.js"></script>
<script src="https://unpkg.com/feather-icons"></script>
<script src="/js/fresh.js"></script>
<script src="/js/jquery.panelslider.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/modernizr/2.8.3/modernizr.min.js"></script>

  </body>
</html>
